import SwiftUI

// 饲料计算器视图
struct FeedCalculatorView: View {
    @State private var selectedAnimalType: String = "cattle"
    @State private var animalAge: Double = 12
    @State private var animalWeight: Double = 500
    @State private var numberOfAnimals: Int = 1
    @State private var feedRequirement: Double = 0
    @State private var feedType: String = "grain"
    
    let animalTypes = [
        ("cattle", "牛", "🐄"),
        ("sheep", "羊", "🐑"),
        ("pig", "猪", "🐷"),
        ("chicken", "鸡", "🐔"),
        ("duck", "鸭", "🦆"),
        ("horse", "马", "🐎")
    ]
    
    let feedTypes = [
        ("grain", "谷物"),
        ("hay", "干草"),
        ("silage", "青贮饲料"),
        ("concentrate", "浓缩饲料")
    ]
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text("饲料需求计算器")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)
                
                Text("根据动物类型、年龄和体重计算每日饲料需求")
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 参数输入卡片
            VStack(spacing: 20) {
                // 动物类型选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("动物类型")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(animalTypes, id: \.0) { type in
                                AnimalTypeButton(
                                    icon: type.2,
                                    name: type.1,
                                    isSelected: selectedAnimalType == type.0,
                                    action: {
                                        selectedAnimalType = type.0
                                        calculateFeedRequirement()
                                    }
                                )
                            }
                        }
                    }
                }
                
                // 动物年龄
                VStack(alignment: .leading, spacing: 8) {
                    Text("年龄（月）")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    HStack {
                        Slider(value: $animalAge, in: 1...60, step: 1)
                            .onChange(of: animalAge) { _ in
                                calculateFeedRequirement()
                            }
                        
                        Text("\(Int(animalAge))月")
                            .font(.system(size: 16))
                            .foregroundColor(.black)
                            .frame(width: 60)
                    }
                }
                
                // 动物体重
                VStack(alignment: .leading, spacing: 8) {
                    Text("体重（公斤）")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    HStack {
                        Slider(value: $animalWeight, in: 1...1000, step: 1)
                            .onChange(of: animalWeight) { _ in
                                calculateFeedRequirement()
                            }
                        
                        Text("\(Int(animalWeight))公斤")
                            .font(.system(size: 16))
                            .foregroundColor(.black)
                            .frame(width: 80)
                    }
                }
                
                // 动物数量
                VStack(alignment: .leading, spacing: 8) {
                    Text("数量")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    HStack {
                        Button(action: { 
                            if numberOfAnimals > 1 {
                                numberOfAnimals -= 1
                                calculateFeedRequirement()
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black.opacity(0.8))
                        }
                        
                        Text("\(numberOfAnimals)")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.black)
                            .frame(width: 40)
                        
                        Button(action: { 
                            numberOfAnimals += 1
                            calculateFeedRequirement()
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black)
                        }
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 饲料类型
            VStack(alignment: .leading, spacing: 8) {
                Text("饲料类型")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                Picker("饲料类型", selection: $feedType) {
                    ForEach(feedTypes, id: \.0) { type in
                        Text(type.1).tag(type.0)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: feedType) { _ in
                    calculateFeedRequirement()
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 计算结果卡片
            VStack(spacing: 16) {
                Text("每日饲料需求")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.black)
                
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        // 单个动物
                        HStack {
                            Text("单个动物:")
                                .font(.system(size: 16))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text(String(format: "%.2f 公斤", feedRequirement))
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                        }
                        
                        // 全部动物
                        if numberOfAnimals > 1 {
                            HStack {
                                Text("全部动物:")
                                    .font(.system(size: 16))
                                    .foregroundColor(Color.black.opacity(0.7))
                                
                                Spacer()
                                
                                Text(String(format: "%.2f 公斤", feedRequirement * Double(numberOfAnimals)))
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.black)
                            }
                        }
                        
                        // 每周
                        HStack {
                            Text("每周需求:")
                                .font(.system(size: 16))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text(String(format: "%.2f 公斤", feedRequirement * Double(numberOfAnimals) * 7))
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                        }
                        
                        // 每月
                        HStack {
                            Text("每月需求:")
                                .font(.system(size: 16))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text(String(format: "%.2f 公斤", feedRequirement * Double(numberOfAnimals) * 30))
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                        }
                    }
                }
                .padding(.top, 8)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 饲料建议卡片
            VStack(alignment: .leading, spacing: 12) {
                Text("饲料建议")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                Text(getFeedingAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .onAppear {
            calculateFeedRequirement()
        }
    }
    
    // 计算饲料需求
    private func calculateFeedRequirement() {
        // 基于动物类型、年龄和体重计算每日饲料需求
        // 这里使用简化的计算公式，实际应用中可以使用更复杂的营养学公式
        
        var baseRequirement: Double = 0
        
        switch selectedAnimalType {
        case "cattle":
            // 牛的饲料需求：体重的2-3%
            let ageFactorMultiplier = animalAge < 12 ? 0.03 : (animalAge < 24 ? 0.025 : 0.02)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "sheep":
            // 羊的饲料需求：体重的2-4%
            let ageFactorMultiplier = animalAge < 6 ? 0.04 : (animalAge < 12 ? 0.03 : 0.02)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "pig":
            // 猪的饲料需求：体重的3-5%
            let ageFactorMultiplier = animalAge < 3 ? 0.05 : (animalAge < 6 ? 0.04 : 0.03)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "chicken":
            // 鸡的饲料需求（以克计）：体重的5-8%
            let ageFactorMultiplier = animalAge < 1 ? 0.08 : (animalAge < 3 ? 0.07 : 0.05)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "duck":
            // 鸭的饲料需求：体重的5-8%
            let ageFactorMultiplier = animalAge < 1 ? 0.08 : (animalAge < 3 ? 0.07 : 0.05)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "horse":
            // 马的饲料需求：体重的1.5-2.5%
            let ageFactorMultiplier = animalAge < 12 ? 0.025 : (animalAge < 36 ? 0.02 : 0.015)
            baseRequirement = animalWeight * ageFactorMultiplier
        default:
            baseRequirement = animalWeight * 0.025
        }
        
        // 根据饲料类型调整
        switch feedType {
        case "grain":
            feedRequirement = baseRequirement * 0.4  // 谷物通常是总需求的30-50%
        case "hay":
            feedRequirement = baseRequirement * 0.8  // 干草通常是反刍动物主要饲料
        case "silage":
            feedRequirement = baseRequirement * 1.2  // 青贮饲料水分含量高，需要更多重量
        case "concentrate":
            feedRequirement = baseRequirement * 0.3  // 浓缩饲料营养密度高，需要较少量
        default:
            feedRequirement = baseRequirement
        }
    }
    
    // 获取饲养建议
    private func getFeedingAdvice() -> String {
        var advice = ""
        
        switch selectedAnimalType {
        case "cattle":
            if animalAge < 6 {
                advice = "幼龄牛需要高蛋白饲料，建议提供优质的初生牛犊饲料。每天分3-4次喂食，确保有足够的清水。"
            } else if animalAge < 12 {
                advice = "生长期牛需要均衡的饮食，包括足够的蛋白质和能量。建议饲喂优质干草、粗饲料和适量精饲料。"
            } else {
                advice = "成年牛可以主要以粗饲料为主，根据生产状态补充精饲料。确保饲料新鲜，饮水充足。"
            }
        case "sheep":
            advice = "羊需要优质粗饲料，特别是好的干草。如果放牧，确保充足的草场面积。补充矿物质，特别是铜和硒。"
        case "pig":
            if animalAge < 3 {
                advice = "仔猪需要高质量的初生料，富含蛋白质和必需氨基酸。每天多次少量喂食。"
            } else {
                advice = "生长育肥猪需要平衡的日粮，包括谷物、蛋白质补充剂和矿物质。控制喂食量以防止过度肥胖。"
            }
        case "chicken":
            advice = "鸡需要全价配合饲料，包含适当水平的蛋白质、能量、维生素和矿物质。蛋鸡需要额外的钙质。"
        case "duck":
            advice = "鸭子饲料应含有足够的蛋白质和能量。如有条件，可提供水生环境和部分绿色饲料。"
        case "horse":
            advice = "马以优质干草为主要饲料，辅以适量的谷物。饲喂次数应多，每次量少。提供盐块和矿物质补充剂。"
        default:
            advice = "请根据动物种类和生长阶段提供均衡的饮食，确保充足的饮水。"
        }
        
        return advice
    }
} 