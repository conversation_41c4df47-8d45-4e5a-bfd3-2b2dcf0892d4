import SwiftUI

// 生产追踪视图
struct ProductionTrackerView: View {
    @State private var selectedAnimalType: ProductionAnimalType = .cow
    @State private var animalCount: Int = 10
    @State private var productionDays: Int = 30
    @State private var totalProduction: Double = 0
    @State private var productionHistory: [ProductionRecord] = []
    @State private var averagePerAnimal: Double = 0
    @State private var productionTrend: String = "稳定"
    @State private var showingAddRecord = false
    @State private var newProductionDate = Date()
    @State private var newProductionAmount: Double = 0
    
    enum ProductionAnimalType: String, CaseIterable, Identifiable {
        case cow = "cow"
        case goat = "goat"
        case sheep = "sheep"
        case chicken = "chicken"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .cow: return "奶牛"
            case .goat: return "奶山羊"
            case .sheep: return "奶绵羊"
            case .chicken: return "蛋鸡"
            }
        }
        
        var productName: String {
            switch self {
            case .cow, .goat, .sheep: return "奶"
            case .chicken: return "蛋"
            }
        }
        
        var productUnit: String {
            switch self {
            case .cow, .goat, .sheep: return "升"
            case .chicken: return "枚"
            }
        }
        
        var icon: String {
            switch self {
            case .cow: return "🐄"
            case .goat: return "🐐"
            case .sheep: return "🐑"
            case .chicken: return "🐔"
            }
        }
        
        var expectedProduction: ClosedRange<Double> {
            switch self {
            case .cow: return 15...40 // 每头牛每天15-40升牛奶
            case .goat: return 2...5 // 每只山羊每天2-5升羊奶
            case .sheep: return 1...3 // 每只绵羊每天1-3升羊奶
            case .chicken: return 0.7...1.0 // 每只鸡每天0.7-1.0枚鸡蛋
            }
        }
    }
    
    struct ProductionRecord: Identifiable {
        let id = UUID()
        let date: Date
        let amount: Double
        let animalType: ProductionAnimalType
        let animalCount: Int
        
        var averagePerAnimal: Double {
            return animalCount > 0 ? amount / Double(animalCount) : 0
        }
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text("生产追踪计算器")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)
                
                Text("追踪动物的产奶量或产蛋量")
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 动物类型选择卡片
            VStack(spacing: 20) {
                Text("动物类型")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack(spacing: 10) {
                    ForEach(ProductionAnimalType.allCases) { type in
                        Button(action: {
                            selectedAnimalType = type
                            generateSampleData()
                            calculateProduction()
                        }) {
                            VStack(spacing: 8) {
                                Text(type.icon)
                                    .font(.system(size: 24))
                                
                                Text(type.displayName)
                                    .font(.system(size: 14, weight: selectedAnimalType == type ? .semibold : .regular))
                                    .foregroundColor(selectedAnimalType == type ? .black : Color.black.opacity(0.6))
                            }
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .background(selectedAnimalType == type ? Color.black.opacity(0.05) : Color.clear)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(selectedAnimalType == type ? Color.black.opacity(0.1) : Color.clear, lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                
                // 动物数量
                VStack(alignment: .leading, spacing: 8) {
                    Text("动物数量")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))
                    
                    HStack {
                        Button(action: { 
                            if animalCount > 1 {
                                animalCount -= 1
                                calculateProduction()
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black.opacity(0.8))
                        }
                        
                        Text("\(animalCount)")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.black)
                            .frame(width: 40, alignment: .center)
                        
                        Button(action: { 
                            animalCount += 1
                            calculateProduction()
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black)
                        }
                    }
                }
                
                // 统计周期
                VStack(alignment: .leading, spacing: 8) {
                    Text("统计周期（天）")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))
                    
                    Picker("统计周期", selection: $productionDays) {
                        Text("7天").tag(7)
                        Text("14天").tag(14)
                        Text("30天").tag(30)
                        Text("90天").tag(90)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .onChange(of: productionDays) { _ in
                        generateSampleData()
                        calculateProduction()
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 生产统计卡片
            VStack(spacing: 20) {
                HStack {
                    Text("生产统计")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    Button(action: { showingAddRecord = true }) {
                        Label("添加记录", systemImage: "plus")
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                    }
                }
                
                VStack(spacing: 16) {
                    // 总产量
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("总产量")
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Text("\(String(format: "%.1f", totalProduction)) \(selectedAnimalType.productUnit)")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(.black)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("平均每只")
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Text("\(String(format: "%.1f", averagePerAnimal)) \(selectedAnimalType.productUnit)/天")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(.black)
                        }
                    }
                    
                    // 趋势指示器
                    HStack {
                        Text("生产趋势:")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        Text(productionTrend)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(getTrendColor())
                        
                        Image(systemName: getTrendIcon())
                            .foregroundColor(getTrendColor())
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // 预期产量对比
                    VStack(alignment: .leading, spacing: 8) {
                        Text("与预期产量对比")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        HStack {
                            Text("每只每天: \(String(format: "%.1f", averagePerAnimal)) \(selectedAnimalType.productUnit)")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                            
                            Spacer()
                            
                            Text("预期: \(String(format: "%.1f", selectedAnimalType.expectedProduction.lowerBound))-\(String(format: "%.1f", selectedAnimalType.expectedProduction.upperBound)) \(selectedAnimalType.productUnit)")
                                .font(.system(size: 14))
                                .foregroundColor(isWithinExpectedRange() ? .green : .orange)
                        }
                    }
                }
                .padding(.vertical, 8)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 历史记录卡片
            VStack(alignment: .leading, spacing: 12) {
                Text("最近记录")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                if productionHistory.isEmpty {
                    Text("暂无生产记录")
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.5))
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.vertical, 20)
                } else {
                    ForEach(Array(productionHistory.prefix(5).enumerated()), id: \.element.id) { index, record in
                        HStack {
                            Text(formatDate(record.date))
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text("\(String(format: "%.1f", record.amount)) \(record.animalType.productUnit)")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                            
                            Text("(\(String(format: "%.1f", record.averagePerAnimal)) \(record.animalType.productUnit)/只)")
                                .font(.system(size: 12))
                                .foregroundColor(Color.black.opacity(0.5))
                        }
                        .padding(.vertical, 8)
                        
                        if index < min(4, productionHistory.count - 1) {
                            Divider()
                                .background(Color.black.opacity(0.1))
                        }
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 生产管理提示卡片
            VStack(alignment: .leading, spacing: 12) {
                Text("生产管理提示")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                Text(getProductionAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .sheet(isPresented: $showingAddRecord) {
            AddProductionRecordView(
                animalType: selectedAnimalType,
                animalCount: animalCount,
                onSave: { date, amount in
                    let newRecord = ProductionRecord(
                        date: date,
                        amount: amount,
                        animalType: selectedAnimalType,
                        animalCount: animalCount
                    )
                    productionHistory.insert(newRecord, at: 0)
                    calculateProduction()
                }
            )
        }
        .onAppear {
            generateSampleData()
            calculateProduction()
        }
    }
    
    // 生成示例数据
    private func generateSampleData() {
        // 清空现有数据
        productionHistory.removeAll()
        
        // 生成样本数据，用于演示
        let calendar = Calendar.current
        let today = Date()
        var dailyAverage: Double
        
        switch selectedAnimalType {
        case .cow:
            dailyAverage = 25.0 // 每天平均25升牛奶
        case .goat:
            dailyAverage = 3.5 // 每天平均3.5升羊奶
        case .sheep:
            dailyAverage = 2.0 // 每天平均2升羊奶
        case .chicken:
            dailyAverage = 0.85 // 每天平均0.85枚鸡蛋
        }
        
        // 生成过去 productionDays 天的记录
        for day in 0..<productionDays {
            if let recordDate = calendar.date(byAdding: .day, value: -day, to: today) {
                // 添加一些随机变化，使数据更真实
                let randomFactor = Double.random(in: 0.9...1.1)
                let amount = dailyAverage * Double(animalCount) * randomFactor
                
                // 创建记录
                let record = ProductionRecord(
                    date: recordDate,
                    amount: amount,
                    animalType: selectedAnimalType,
                    animalCount: animalCount
                )
                
                productionHistory.append(record)
            }
        }
        
        // 按日期排序
        productionHistory.sort { $0.date > $1.date }
    }
    
    // 计算生产统计数据
    private func calculateProduction() {
        guard !productionHistory.isEmpty else {
            totalProduction = 0
            averagePerAnimal = 0
            productionTrend = "无数据"
            return
        }
        
        // 计算总产量
        totalProduction = productionHistory.reduce(0) { $0 + $1.amount }
        
        // 计算每只动物的平均每日产量
        let totalDays = min(productionDays, productionHistory.count)
        if totalDays > 0 && animalCount > 0 { // Ensure no division by zero
            let avgPerDay = totalProduction / Double(totalDays)
            averagePerAnimal = avgPerDay / Double(animalCount)
        } else {
            averagePerAnimal = 0
        }
        
        // 分析趋势（比较前半段和后半段的平均值）
        if productionHistory.count >= 4 {
            let halfPoint = productionHistory.count / 2
            let firstHalf = productionHistory.suffix(from: halfPoint)
            let secondHalf = productionHistory.prefix(halfPoint)
            
            let firstHalfAvg = firstHalf.reduce(0) { $0 + $1.amount } / Double(firstHalf.count)
            let secondHalfAvg = secondHalf.reduce(0) { $0 + $1.amount } / Double(secondHalf.count)
            
            if firstHalfAvg > 0 { // Ensure no division by zero
                let percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                if percentChange > 5 {
                    productionTrend = "上升"
                } else if percentChange < -5 {
                    productionTrend = "下降"
                } else {
                    productionTrend = "稳定"
                }
            } else {
                 productionTrend = "稳定" // If first half avg is 0, consider stable or insufficient data
            }
        } else {
            productionTrend = "数据不足"
        }
    }
    
    // 获取趋势图标
    private func getTrendIcon() -> String {
        switch productionTrend {
        case "上升":
            return "arrow.up.circle.fill"
        case "下降":
            return "arrow.down.circle.fill"
        case "稳定":
            return "equal.circle.fill"
        default:
            return "questionmark.circle.fill"
        }
    }
    
    // 获取趋势颜色
    private func getTrendColor() -> Color {
        switch productionTrend {
        case "上升":
            return .green
        case "下降":
            return .red
        case "稳定":
            return .blue
        default:
            return Color.black.opacity(0.5)
        }
    }
    
    // 检查是否在预期产量范围内
    private func isWithinExpectedRange() -> Bool {
        let range = selectedAnimalType.expectedProduction
        return range.contains(averagePerAnimal)
    }
    
    // 获取生产管理建议
    private func getProductionAdvice() -> String {
        switch selectedAnimalType {
        case .cow:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return "当前产奶量低于预期。建议检查饲料质量和饮食配比，确保饲喂足够的优质粗饲料和浓缩饲料。注意牛舍环境，减少应激，提高舒适度。定期检查牛只健康状况，预防乳房炎等疾病。"
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return "产奶量表现优异！继续保持当前的饲养管理方法。记得监控奶牛健康状况，避免因过度产奶造成营养消耗。确保饲料中含有足够的能量和蛋白质。"
            } else {
                return "产奶量处于正常范围。保持均衡的饲养管理，包括适当的饲料配比、充足的饮水和舒适的环境。定期检查牛群健康，保持挤奶设备清洁卫生。"
            }
        case .goat, .sheep:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return "当前产奶量低于预期。建议改善饲养条件，提供优质饲料和清洁饮水。检查动物健康状况，尤其是乳房健康。调整挤奶频率和技术，保持挤奶环境安静舒适。"
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return "产奶量表现优异！继续保持当前的饲养管理方法。监控动物的体况，确保不会因高产导致体重下降过快。补充足够的营养物质。"
            } else {
                return "产奶量处于正常范围。维持良好的饲养管理，包括适当的饲料配比和舒适的环境。定期检查健康状况，保持挤奶设备的清洁和正确使用。"
            }
        case .chicken:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return "当前产蛋量低于预期。建议检查饲料质量，确保蛋鸡获得足够的蛋白质、钙质和其他必要营养素。控制光照时间（通常14-16小时光照最理想）。检查鸡舍环境，减少应激因素。"
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return "产蛋量表现优异！继续保持当前的饲养管理方法。确保提供足够的高钙饲料，支持持续的蛋壳形成。定期更换垫料，保持鸡舍干燥清洁。"
            } else {
                return "产蛋量处于正常范围。保持良好的饲养管理，包括均衡饲料、充足的饮水和适宜的环境温度。保持鸡舍安静，减少惊扰和应激。定期收集鸡蛋，防止鸡开始抱窝。"
            }
        }
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd"
        return formatter.string(from: date)
    }
} 