import SwiftUI

// 土地管理视图
struct LandManagementView: View {
    @State private var landArea: Double = 10
    @State private var selectedAreaUnit: AreaUnit = .mu
    @State private var selectedResourceType: ResourceType = .fertilizer
    @State private var resourceAmount: Double = 100
    @State private var resourceUnit: String = "公斤"
    @State private var calculationResult: Double = 0
    @State private var selectedFertilizerType: FertilizerType = .compound
    @State private var selectedSeedType: SeedType = .corn
    
    enum AreaUnit: String, CaseIterable, Identifiable {
        case mu = "mu"
        case hectare = "hectare"
        case acre = "acre"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .mu: return "亩"
            case .hectare: return "公顷"
            case .acre: return "英亩"
            }
        }
        
        var conversionToSquareMeter: Double {
            switch self {
            case .mu: return 666.7
            case .hectare: return 10000
            case .acre: return 4046.86
            }
        }
    }
    
    enum ResourceType: String, CaseIterable, Identifiable {
        case fertilizer = "fertilizer"
        case seed = "seed"
        case water = "water"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .fertilizer: return "肥料"
            case .seed: return "种子"
            case .water: return "灌溉水"
            }
        }
        
        var unit: String {
            switch self {
            case .fertilizer: return "公斤"
            case .seed: return "公斤"
            case .water: return "立方米"
            }
        }
    }
    
    enum FertilizerType: String, CaseIterable, Identifiable {
        case nitrogen = "nitrogen"
        case phosphorus = "phosphorus"
        case potassium = "potassium"
        case compound = "compound"
        case organic = "organic"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .nitrogen: return "氮肥"
            case .phosphorus: return "磷肥"
            case .potassium: return "钾肥"
            case .compound: return "复合肥"
            case .organic: return "有机肥"
            }
        }
        
        var recommendedRate: ClosedRange<Double> {
            switch self {
            case .nitrogen: return 15...30
            case .phosphorus: return 20...40
            case .potassium: return 15...30
            case .compound: return 30...60
            case .organic: return 100...300
            }
        }
    }
    
    enum SeedType: String, CaseIterable, Identifiable {
        case corn = "corn"
        case wheat = "wheat"
        case rice = "rice"
        case soybean = "soybean"
        case alfalfa = "alfalfa"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .corn: return "玉米"
            case .wheat: return "小麦"
            case .rice: return "水稻"
            case .soybean: return "大豆"
            case .alfalfa: return "苜蓿"
            }
        }
        
        var recommendedRate: ClosedRange<Double> {
            switch self {
            case .corn: return 20...30
            case .wheat: return 100...150
            case .rice: return 10...15
            case .soybean: return 60...80
            case .alfalfa: return 15...25
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text("土地管理计算器")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)
                
                Text("计算每单位面积的资源使用量")
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 土地面积输入卡片
            VStack(spacing: 20) {
                Text("土地面积")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack {
                    TextField("土地面积", value: $landArea, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: landArea) { _ in
                            calculateResource()
                        }
                    
                    Picker("面积单位", selection: $selectedAreaUnit) {
                        ForEach(AreaUnit.allCases) { unit in
                            Text(unit.displayName).tag(unit)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(width: 100)
                    .onChange(of: selectedAreaUnit) { _ in
                        calculateResource()
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 资源类型选择卡片
            VStack(spacing: 20) {
                Text("资源类型")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack(spacing: 10) {
                    ForEach(ResourceType.allCases) { type in
                        Button(action: {
                            selectedResourceType = type
                            resourceUnit = type.unit
                            calculateResource()
                        }) {
                            Text(type.displayName)
                                .font(.system(size: 14, weight: selectedResourceType == type ? .semibold : .regular))
                                .foregroundColor(selectedResourceType == type ? .white : .black)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(selectedResourceType == type ? Color.black : Color.white)
                                .cornerRadius(20)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(Color.black.opacity(0.1), lineWidth: selectedResourceType == type ? 0 : 1)
                                )
                        }
                    }
                }
                
                // 肥料类型选择（仅当选择肥料时显示）
                if selectedResourceType == .fertilizer {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("肥料类型")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        Picker("肥料类型", selection: $selectedFertilizerType) {
                            ForEach(FertilizerType.allCases) { type in
                                Text(type.displayName).tag(type)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: selectedFertilizerType) { _ in
                            calculateResource()
                        }
                    }
                }
                
                // 种子类型选择（仅当选择种子时显示）
                if selectedResourceType == .seed {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("作物类型")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        Picker("作物类型", selection: $selectedSeedType) {
                            ForEach(SeedType.allCases) { type in
                                Text(type.displayName).tag(type)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: selectedSeedType) { _ in
                            calculateResource()
                        }
                    }
                }
                
                // 资源总量输入
                VStack(alignment: .leading, spacing: 12) {
                    Text("资源总量")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))
                    
                    HStack {
                        TextField("资源总量", value: $resourceAmount, formatter: NumberFormatter())
                            .keyboardType(.decimalPad)
                            .padding()
                            .background(Color.white)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
                            )
                            .onChange(of: resourceAmount) { _ in
                                calculateResource()
                            }
                        
                        Text(resourceUnit)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))
                            .frame(width: 60)
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 计算结果卡片
            VStack(spacing: 20) {
                Text("计算结果")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                VStack(spacing: 16) {
                    // 每单位面积的资源用量
                    HStack {
                        Text("每\(selectedAreaUnit.displayName)用量:")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        Spacer()
                        
                        Text(String(format: "%.2f %@", calculationResult, resourceUnit))
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.black)
                    }
                    
                    // 推荐用量（仅在适用时显示）
                    if let recommendedRange = getRecommendedRange() {
                        HStack {
                            Text("推荐用量范围:")
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text(String(format: "%.1f - %.1f %@", recommendedRange.lowerBound, recommendedRange.upperBound, resourceUnit))
                                .font(.system(size: 14))
                                .foregroundColor(isWithinRecommendedRange() ? .green : .orange)
                        }
                        
                        // 如果计算结果不在推荐范围内，显示提示
                        if !isWithinRecommendedRange() {
                            Text(calculationResult < recommendedRange.lowerBound ? "当前用量低于推荐值，可能影响产量" : "当前用量超过推荐值，可能造成浪费或环境影响")
                                .font(.system(size: 14))
                                .foregroundColor(.orange)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(.top, 4)
                        }
                    }
                    
                    // 总覆盖面积（当输入资源总量时）
                    HStack {
                        Text("可覆盖总面积:")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        Spacer()
                        
                        Text(String(format: "%.2f %@", resourceAmount / calculationResult, selectedAreaUnit.displayName))
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                    }
                }
                .padding(.vertical, 8)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 使用建议卡片
            VStack(alignment: .leading, spacing: 12) {
                Text("使用建议")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                Text(getUsageAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .onAppear {
            calculateResource()
        }
    }
    
    // 计算每单位面积的资源使用量
    private func calculateResource() {
        if landArea > 0 {
            calculationResult = resourceAmount / landArea
        } else {
            calculationResult = 0
        }
    }
    
    // 获取推荐用量范围
    private func getRecommendedRange() -> ClosedRange<Double>? {
        switch selectedResourceType {
        case .fertilizer:
            return selectedFertilizerType.recommendedRate
        case .seed:
            return selectedSeedType.recommendedRate
        case .water:
            // 水的推荐灌溉量根据不同的作物和土壤类型有很大差异
            // 这里提供一个非常粗略的估计
            return 30...60
        }
    }
    
    // 检查计算结果是否在推荐范围内
    private func isWithinRecommendedRange() -> Bool {
        if let range = getRecommendedRange() {
            return range.contains(calculationResult)
        }
        return true
    }
    
    // 获取使用建议
    private func getUsageAdvice() -> String {
        switch selectedResourceType {
        case .fertilizer:
            switch selectedFertilizerType {
            case .nitrogen:
                return "氮肥主要促进植物茎叶生长。建议分次施用，避免一次性大量施用造成养分流失。春季作物生长期是施用的最佳时期。注意过量施用可能导致地下水污染。"
            case .phosphorus:
                return "磷肥有助于促进根系发展和花果形成。建议在土壤耕作时施入土壤下层，因为磷在土壤中移动性较差。酸性土壤中磷的有效性较低，可能需要调整用量。"
            case .potassium:
                return "钾肥能提高作物抗病性和抗逆性。在作物开花结果前施用效果最佳。砂质土壤钾容易流失，可能需要增加施用量或分次施用。"
            case .compound:
                return "复合肥含有多种养分，方便一次性施用。建议根据土壤检测结果选择适合的NPK比例。一般在作物生长初期施用，确保养分均衡供应。"
            case .organic:
                return "有机肥改善土壤结构，增加土壤有机质含量，促进土壤微生物活动。建议提前堆沤腐熟后再使用。有机肥养分释放较慢，需要提前施用。"
            }
        case .seed:
            switch selectedSeedType {
            case .corn:
                return "玉米种子应在土壤温度稳定在10°C以上时播种。播种深度约3-5厘米，行距60-75厘米。确保播种均匀，避免漏播和重播。"
            case .wheat:
                return "小麦适宜在秋季或早春播种。播种深度约2-3厘米，行距15-20厘米。播种前应处理种子防病害，播后适当镇压以促进出芽。"
            case .rice:
                return "水稻通常采用育苗移栽或直播。直播时，播种量应根据品种特性调整。插秧时，每穴插2-3株，株距约15-20厘米，行距约25-30厘米。"
            case .soybean:
                return "大豆适宜在土壤温度达到15°C以上时播种。播种深度约2-4厘米，行距40-60厘米。大豆种子较易腐烂，播种前可进行种子处理。"
            case .alfalfa:
                return "苜蓿种子小，播种深度不宜过深，约1-2厘米。播种前应整地细致，播后轻轻镇压。苜蓿对pH值敏感，酸性土壤应先进行石灰调节。"
            }
        case .water:
            return "灌溉用水量应根据作物生长阶段、气候条件和土壤类型调整。建议采用滴灌或微喷灌等节水灌溉技术。灌溉时间最好选在清晨或傍晚，减少水分蒸发。避免过度灌溉，以防根系缺氧和土壤养分流失。"
        }
    }
} 