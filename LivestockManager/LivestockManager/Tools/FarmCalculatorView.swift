import SwiftUI

struct FarmCalculatorView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedCalculator: CalculatorType = .feedCalculator
    
    enum CalculatorType: String, CaseIterable, Identifiable {
        case feedCalculator = "feedCalculator"
        case breedingCalculator = "breedingCalculator"
        case costAnalyzer = "costAnalyzer"
        case landManagement = "landManagement"
        case productionTracker = "productionTracker"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .feedCalculator: return L.Tools.Calculator.feed // Assuming L. is a localization helper
            case .breedingCalculator: return L.Tools.Calculator.breeding
            case .costAnalyzer: return L.Tools.Calculator.cost
            case .landManagement: return L.Tools.Calculator.land
            case .productionTracker: return L.Tools.Calculator.production
            }
        }
        
        var systemImage: String {
            switch self {
            case .feedCalculator: return "pawprint.fill"
            case .breedingCalculator: return "heart.fill"
            case .costAnalyzer: return "dollarsign.circle.fill"
            case .landManagement: return "leaf.fill"
            case .productionTracker: return "chart.bar.fill"
            }
        }
        
        // Using the user's updated descriptions directly
        var description: String {
            switch self {
            case .feedCalculator: return "根据动物类型、年龄和体重计算每日饲料需求"
            case .breedingCalculator: return "计算妊娠期和预产期"
            case .costAnalyzer: return "分析饲料、护理和销售成本"
            case .landManagement: return "计算肥料需求和播种率"
            case .productionTracker: return "追踪产奶量和产蛋量"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    Button(action: { dismiss() }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.black)
                    }
                    
                    Spacer()
                    
                    Text(L.Tools.calculator) // Assuming L. is a localization helper
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    Color.clear.frame(width: 16, height: 16)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(Color.white)
                
                // 计算器选择区域
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(CalculatorType.allCases) { calculatorType in
                            CalculatorTabButton(
                                type: calculatorType,
                                isSelected: selectedCalculator == calculatorType,
                                action: { selectedCalculator = calculatorType }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                }
                .background(Color.white)
                
                // 当前选择的计算器
                ScrollView {
                    VStack(spacing: 20) {
                        switch selectedCalculator {
                        case .feedCalculator:
                            FeedCalculatorView() // This view should be defined elsewhere or moved
                        case .breedingCalculator:
                            BreedingCalculatorView() // This view should be defined elsewhere or moved
                        case .costAnalyzer:
                            CostAnalyzerView() // This view should be defined elsewhere or moved
                        case .landManagement:
                            LandManagementView() // This view should be defined elsewhere or moved
                        case .productionTracker:
                            ProductionTrackerView() // This view should be defined elsewhere or moved
                        }
                        
                        // 底部安全区域
                        Color.clear.frame(height: 40)
                    }
                    .padding(16)
                }
                .background(Color(hex: "#F8F8F8")) // Assuming Color(hex:) is defined and accessible
            }
            .navigationBarHidden(true)
        }
    }
}

// 计算器选项卡按钮
struct CalculatorTabButton: View {
    let type: FarmCalculatorView.CalculatorType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: type.systemImage)
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .black : Color.black.opacity(0.6))
                
                Text(type.displayName)
                    .font(.system(size: 12, weight: isSelected ? .semibold : .regular))
                    .foregroundColor(isSelected ? .black : Color.black.opacity(0.6))
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 14)
            .background(isSelected ? Color.black.opacity(0.05) : Color.clear)
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    FarmCalculatorView()
}

// Assuming L.Tools.Calculator and Color(hex:) are defined elsewhere.
// For example:
// struct L {
//    struct Tools {
//        static let calculator = "农场计算器"
//        struct Calculator {
//            static let feed = "饲料计算"
//            static let breeding = "繁育计算"
//            static let cost = "成本分析"
//            static let land = "土地管理"
//            static let production = "生产追踪"
//        }
//    }
// }
// extension Color {
//    init(hex: String) { ... }
// } 